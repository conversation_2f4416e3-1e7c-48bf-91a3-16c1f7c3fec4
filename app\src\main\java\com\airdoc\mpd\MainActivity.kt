package com.airdoc.mpd

import android.os.Bundle
import android.os.Looper
import android.text.TextUtils
import android.util.Size
import android.util.SparseArray
import android.view.Gravity
import android.view.View
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.core.resolutionselector.ResolutionStrategy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.source.ConcatenatingMediaSource
import androidx.media3.exoplayer.source.MediaSource
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.camera.CameraXViewModel
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.common.MultiClickListener
import com.airdoc.mpd.config.ConfigActivity
import com.airdoc.mpd.databinding.ActivityMainBinding
import com.airdoc.mpd.device.DeviceManager
import com.airdoc.mpd.device.StartupModeSettingsDialog
import com.airdoc.mpd.device.bean.DeviceInfo
import com.airdoc.mpd.device.enumeration.StartupMode.*
import com.airdoc.mpd.device.vm.DeviceViewModel
import com.airdoc.mpd.face.FaceDetectorProcessor
import com.airdoc.mpd.home.DetectionCodeDetectionFragment
import com.airdoc.mpd.home.DeviceExceptionFragment
import com.airdoc.mpd.home.InputInfoDetectionFragment
import com.airdoc.mpd.home.ScanCodeDetectionFragment
import com.airdoc.mpd.media.PlayManager
import com.airdoc.mpd.media.bean.RawMedia
import com.airdoc.mpd.update.UpdateDialog
import com.airdoc.mpd.update.UpdateManager
import com.airdoc.mpd.update.bean.AppUpdateInfo
import com.airdoc.mpd.update.vm.UpdateViewModel
import com.airdoc.mpd.detection.DetectionActivity
import com.google.mlkit.vision.common.InputImage
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.objs.Bluetooth

class MainActivity : BaseCommonActivity() {

    companion object{
        private val TAG = MainActivity::class.java.simpleName
    }

    private lateinit var binding: ActivityMainBinding

    private val viewConfig by id<View>(R.id.view_config)

    private val cameraXVM by viewModels<CameraXViewModel>()
    private val updateVM by viewModels<UpdateViewModel>()
    private val deviceVM by viewModels<DeviceViewModel>()

    private val handler = LifecycleHandler(Looper.getMainLooper(),this)

    private val resolutionSelector = ResolutionSelector.Builder()
        .setResolutionStrategy(
            ResolutionStrategy(Size(1920, 1080), ResolutionStrategy.FALLBACK_RULE_NONE)
        ).build()

    private val imageAnalysis = ImageAnalysis.Builder()
        .setResolutionSelector(resolutionSelector)
        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
        .build()

    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA

    private val faceDetector = FaceDetectorProcessor()

    private var detectedFaceStartTime = 0L
    private var noDetectedFaceStartTime = 0L
    private var isPlayedSpeechGuide = false
    private var focalLengthPx = 0f

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initObserver()
        initData()
        initLepuService()

    }

    private fun initLepuService() {
        val checkService = BleServiceHelper.BleServiceHelper.checkService()
        if (!checkService) {
            // 配置原始数据保存路径
            val rawFolders = SparseArray<String>()
            rawFolders.set(Bluetooth.MODEL_ER1, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_PC60FW, "${getExternalFilesDir(null)?.absolutePath}/pc60fw")

            // 初始化服务（注意顺序）
            BleServiceHelper.BleServiceHelper
                .initRawFolder(rawFolders)
                .initService(application)
                .initLog(false)
        }
    }

    override fun onStart() {
        super.onStart()
        cameraXVM.getProcessCameraProvider(this)
    }

    override fun onResume() {
        super.onResume()
        updateVM.getAppUpdateInfo()
        deviceVM.getDeviceBasicInfo(DeviceManager.getLocalStartupMode()?.label.orEmpty())
    }

    private fun initView() {
        initListener()
    }

    private fun initListener() {
        binding.clSetting.setOnSingleClickListener {
            val menuPopupWindow = MenuPopupWindow(this)
            menuPopupWindow.showAsDropDown(binding.clSetting,0, 10.dp2px(this@MainActivity), Gravity.END)
            menuPopupWindow.apply {
                onVersionClick = {
                    updateVM.getAppUpdateInfo()
                }
                onScannerSettingsClick = {
                    StartupModeSettingsDialog(this@MainActivity).apply {
                        onOkClick  = {
                            deviceVM.getDeviceBasicInfo(it.label)
                        }
                    }.show()
                }
                show()
            }
        }
        viewConfig.setOnClickListener(object : MultiClickListener(){
            override fun onClickValid(v: View?) {
                startActivity(ConfigActivity.createIntent(this@MainActivity))
            }
        })
    }

    private fun initObserver() {
        updateVM.appUpdateInfoLiveData.observe(this) {
            showUpdateDialog(it)
            updateSettingReaDot()
        }
        deviceVM.deviceInfoLiveData.observe(this){
            updateDeviceInfo(it)
        }
        cameraXVM.cameraProviderLiveData.observe(this){
            Logger.d(TAG, msg = "cameraProviderLiveData it = $it")
            if (MMKVManager.decodeBool(CommonPreference.ENABLE_PROACTIVE_GREETING) == true){
                cameraProvider = it
                bindAllCameraUseCases()
            }else{
                unBindAllCameraUseCases()
            }
        }
    }

    private fun initData() {
        focalLengthPx = faceDetector.calculateFocalLength(2.77f,3.2f,1980f)
    }

    private fun showUpdateDialog(updateInfo: AppUpdateInfo?){
        updateInfo?.let {
            val url = it.appVersion?.url?:""
            val version = it.appVersion?.version ?: ""
            val introduction = it.appVersion?.introduce?:""
            val appSize = it.appVersion?.appSize?:0
            if (!TextUtils.isEmpty(url)){
                UpdateDialog(this,url,version,introduction, it.forceUpdate?:false,appSize).show()
            }
        }
    }

    private fun updateSettingReaDot(){
        binding.ivSettingRedDot.isVisible = UpdateManager.isCanUpgraded()
    }

    /**
     * 更新设备信息
     */
    private fun updateDeviceInfo(deviceInfo: DeviceInfo?){
        Logger.d(TAG, msg = "updateDeviceInfo deviceInfo = $deviceInfo")
        ImageLoader.loadImageWithPlaceholder(this,deviceInfo?.logo?:"",0,R.drawable.ic_main_logo,binding.ivLogo)
        binding.tvCopyright.text = deviceInfo?.copyright
        if (DeviceManager.isAvailable(deviceInfo)){
            when(DeviceManager.getStartupMode(deviceInfo)){
                QRCODE_WECHAT,QRCODE_H5,QRCODE_WHATSAPP -> {
                    showScanCode()
                }
                ACCESS_CODE -> {
                    showDetectionCode()
                }
                MANUAL_ENTRY -> {
                    showInputInfo()
                }
                else ->{
                    showException()
                }
            }
        }else{
            showException()
        }
    }

    //显示扫码检测
    private fun showScanCode(){
        binding.clMainRoot.setBackgroundResource(R.drawable.ic_main_bg)
        val fragment =
            supportFragmentManager.findFragmentByTag(ScanCodeDetectionFragment.FRAGMENT_TAG_SCAN_CODE_DETECTION)
        if (fragment == null){
            val beginTransaction = supportFragmentManager.beginTransaction()
            beginTransaction.replace(R.id.fl_start_up_mode, ScanCodeDetectionFragment.newInstance(),
                ScanCodeDetectionFragment.FRAGMENT_TAG_SCAN_CODE_DETECTION)
            beginTransaction.commitAllowingStateLoss()
        }
    }

    //显示检测码检测
    private fun showDetectionCode(){
        binding.clMainRoot.setBackgroundResource(R.drawable.ic_main_bg_1)
        val fragment =
            supportFragmentManager.findFragmentByTag(DetectionCodeDetectionFragment.FRAGMENT_TAG_DETECTION_CODE_DETECTION)
        if (fragment == null){
            val beginTransaction = supportFragmentManager.beginTransaction()
            beginTransaction.replace(R.id.fl_start_up_mode, DetectionCodeDetectionFragment.newInstance(),
                DetectionCodeDetectionFragment.FRAGMENT_TAG_DETECTION_CODE_DETECTION)
            beginTransaction.commitAllowingStateLoss()
        }
    }

    //显示输入信息检测
    private fun showInputInfo(){
        binding.clMainRoot.setBackgroundResource(R.drawable.ic_main_bg)
        val fragment =
            supportFragmentManager.findFragmentByTag(InputInfoDetectionFragment.FRAGMENT_TAG_INPUT_INFO_DETECTION)
        if (fragment == null){
            val beginTransaction = supportFragmentManager.beginTransaction()
            beginTransaction.replace(R.id.fl_start_up_mode, InputInfoDetectionFragment.newInstance(),
                InputInfoDetectionFragment.FRAGMENT_TAG_INPUT_INFO_DETECTION)
            beginTransaction.commitAllowingStateLoss()
        }
    }

    private fun showException(){
        binding.clMainRoot.setBackgroundResource(R.drawable.ic_main_bg)
        val fragment =
            supportFragmentManager.findFragmentByTag(DeviceExceptionFragment.FRAGMENT_TAG_DEVICE_EXCEPTION)
        if (fragment == null){
            val beginTransaction = supportFragmentManager.beginTransaction()
            beginTransaction.replace(R.id.fl_start_up_mode, DeviceExceptionFragment.newInstance(),
                DeviceExceptionFragment.FRAGMENT_TAG_DEVICE_EXCEPTION)
            beginTransaction.commitAllowingStateLoss()
        }
    }

    private fun bindAllCameraUseCases(){
        cameraProvider?.let {
            Logger.d(TAG, msg = "bindAllCameraUseCases")
            imageAnalysis.setAnalyzer(ContextCompat.getMainExecutor(this@MainActivity)) { image ->
                handleImageProxy(image)
            }
            try {
                it.unbindAll()
                camera = it.bindToLifecycle(this, cameraSelector,imageAnalysis)
                Logger.d(TAG, msg = "bindToLifecycle camera = $camera")
            } catch (e: Exception) {
                // 处理相机绑定错误
                Logger.e(TAG, msg = "bindAllCameraUseCases Exception = $e")
            }
        }
    }

    private fun unBindAllCameraUseCases(){
        cameraProvider?.let {
            Logger.d(TAG, msg = "unBindAllCameraUseCases")
            try {
                it.unbindAll()
            } catch (e: Exception) {
                // 处理相机绑定错误
                Logger.e(TAG, msg = "unBindAllCameraUseCases Exception = $e")
            }
        }
        cameraProvider = null
        camera = null
    }

    @OptIn(ExperimentalGetImage::class)
    private fun handleImageProxy(imageProxy: ImageProxy){
        val mediaImage  = imageProxy.image
        if (mediaImage != null){
            val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
            faceDetector.detectInImage(image,
                onSuccessListener = { faces ->
                    if (faces.isNotEmpty()){
                        val frontFace = faceDetector.isFrontFace(faces[0])
                        val distanceCm = faceDetector.distanceCm(faces[0], focalLengthPx)
//                        Logger.d(TAG, msg = "handleImageProxy frontFace = $frontFace distanceCm = $distanceCm")
                        if (frontFace && distanceCm < 80){
                            detectedFace()
                        }else{
                            noDetectedFace()
                        }
                    }else{
                        noDetectedFace()
                    }
                },
                onFailureListener = {
                    Logger.e(TAG, msg = "handleImageProxy Exception = $it")
                },
                onCompleteListener = {
                    imageProxy.close()
                }
            )
        }else{
            imageProxy.close()
        }
    }

    private fun detectedFace(){
        noDetectedFaceStartTime = 0L
        when {
            detectedFaceStartTime == 0L -> {
                detectedFaceStartTime = System.currentTimeMillis()
            }
            System.currentTimeMillis() - detectedFaceStartTime > 2000 -> {
                if (!isPlayedSpeechGuide){
                    playSpeechGuide()
                    isPlayedSpeechGuide = true
                }
            }
        }
    }

    private fun noDetectedFace(){
        detectedFaceStartTime = 0L
        when {
            noDetectedFaceStartTime == 0L -> {
                noDetectedFaceStartTime = System.currentTimeMillis()
            }
            System.currentTimeMillis() - noDetectedFaceStartTime > 10000 -> {
                isPlayedSpeechGuide = false
            }
        }
    }

    @OptIn(UnstableApi::class)
    private fun playSpeechGuide(){
        Logger.d(TAG, msg = "playSpeechGuide")
        val mediaSources = mutableListOf<MediaSource>()
        val welcomeUse = RawMedia(R.raw.welcome_use_stress_tolerance_assessment).createMediaSource(this)
        if (welcomeUse != null) mediaSources.add(welcomeUse)
        when(DeviceManager.getStartupMode()){
            QRCODE_WECHAT ->{
                val pleaseUse = RawMedia(R.raw.please_use).createMediaSource(this)
                if (pleaseUse != null) mediaSources.add(pleaseUse)
                val weChat = RawMedia(R.raw.we_chat).createMediaSource(this)
                if (weChat != null) mediaSources.add(weChat)
                val scanCode = RawMedia(R.raw.scan_code).createMediaSource(this)
                if (scanCode != null) mediaSources.add(scanCode)
            }
            QRCODE_H5 ->{
                val pleaseUse = RawMedia(R.raw.please_use).createMediaSource(this)
                if (pleaseUse != null) mediaSources.add(pleaseUse)
                val mobileBrowser = RawMedia(R.raw.mobile_browser).createMediaSource(this)
                if (mobileBrowser != null) mediaSources.add(mobileBrowser)
                val scanCode = RawMedia(R.raw.scan_code).createMediaSource(this)
                if (scanCode != null) mediaSources.add(scanCode)
            }
            QRCODE_WHATSAPP ->{
                val pleaseUse = RawMedia(R.raw.please_use).createMediaSource(this)
                if (pleaseUse != null) mediaSources.add(pleaseUse)
                val whatsapp = RawMedia(R.raw.whatsapp).createMediaSource(this)
                if (whatsapp != null) mediaSources.add(whatsapp)
                val scanCode = RawMedia(R.raw.scan_code).createMediaSource(this)
                if (scanCode != null) mediaSources.add(scanCode)
            }
            ACCESS_CODE ->{
                val enterDetectionCode = RawMedia(R.raw.please_enter_detection_code).createMediaSource(this)
                if (enterDetectionCode != null) mediaSources.add(enterDetectionCode)
            }
            else ->{
            }
        }
        if (mediaSources.isEmpty()) return
        val concatenatingMediaSource = ConcatenatingMediaSource(*mediaSources.toTypedArray())
        PlayManager.playMediaSource(concatenatingMediaSource)
    }

    override fun onDestroy() {
        faceDetector.onDestroy()
        super.onDestroy()
    }

}