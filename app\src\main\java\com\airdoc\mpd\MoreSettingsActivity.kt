package com.airdoc.mpd

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.databinding.ActivityMoreSettingsBinding
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lepu.blepro.event.EventMsgConst
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.objs.Bluetooth
import com.lepu.blepro.objs.BluetoothController

/**
 * FileName: MoreSettingsActivity
 * Author by lilin,Date on 2025/7/14 9:53
 * PS: Not easy to write code, please indicate.
 */
class MoreSettingsActivity : BaseCommonActivity() {

    companion object{
        private val TAG = MoreSettingsActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, MoreSettingsActivity::class.java)
            return intent
        }
    }

    private val supportedModels = intArrayOf(Bluetooth.MODEL_PC60FW)


    private lateinit var binding: ActivityMoreSettingsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMoreSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initObserver()
        initView()
    }

    private fun initObserver() {
        // 监听服务初始化完成
        LiveEventBus.get<Boolean>(EventMsgConst.Ble.EventServiceConnectedAndInterfaceInit)
            .observeSticky(this) {
                startScan()
            }

        // 监听设备发现
        LiveEventBus.get<Bluetooth>(EventMsgConst.Discovery.EventDeviceFound)
            .observe(this) { bluetooth ->
                // 处理发现的设备
                handleDeviceFound(bluetooth)
            }
    }

    private fun startScan() {
        BleServiceHelper.BleServiceHelper.startScan(supportedModels)
    }

    private fun handleDeviceFound(bluetooth: Bluetooth) {
        // 更新设备列表UI
        val devices = BluetoothController.getDevices()
        Logger.i("devices","devices",devices.toString())
//        updateDeviceList(devices)
    }

    private fun initView() {
        initListener()
        binding.switchProactivelyGreet.isChecked = MMKVManager.decodeBool(CommonPreference.ENABLE_PROACTIVE_GREETING) == true
    }

    private fun initListener() {
        binding.switchProactivelyGreet.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            MMKVManager.encodeBool(CommonPreference.ENABLE_PROACTIVE_GREETING, isChecked)
        }
    }

}